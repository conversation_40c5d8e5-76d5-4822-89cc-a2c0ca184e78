# Dynamic Form Positioning System

## Overview

The dynamic form system now supports **advanced field positioning** using CSS Grid while maintaining **100% backward compatibility** with the existing round-robin column layout.

## How It Works

### **Automatic Detection**
- If **any field** has positioning properties (`row`, `column`, `rowSize`, `colSize`), the system uses **positioned grid layout**
- If **no fields** have positioning properties, the system uses **existing round-robin layout** (unchanged behavior)

### **Positioning Properties**

| Property | Default | Description |
|----------|---------|-------------|
| `row` | 1 | Row position (1-based) |
| `column` | 1 | Column position (1-based) |
| `rowSize` | 1 | Number of rows to span vertically |
| `colSize` | 1 | Number of columns to span horizontally |

## Examples

### **Basic Positioning**
```json
{
  "columnNumber": 3,
  "fieldName": [
    {
      "fieldName": "firstName",
      "row": 1,
      "column": 1,
      "rowSize": 1,
      "colSize": 1
    },
    {
      "fieldName": "lastName", 
      "row": 1,
      "column": 2,
      "rowSize": 1,
      "colSize": 1
    },
    {
      "fieldName": "email",
      "row": 2,
      "column": 1,
      "rowSize": 1,
      "colSize": 2
    }
  ]
}
```

**Result Grid:**
```
┌───────────┬───────────┬───────────┐
│ firstName │ lastName  │           │ Row 1
├───────────┴───────────┤           │
│ email (spans 2 cols)  │           │ Row 2
└───────────────────────┴───────────┘
```

### **Field Spanning**
```json
{
  "fieldName": [
    {
      "fieldName": "title",
      "row": 1,
      "column": 1,
      "colSize": 3
    },
    {
      "fieldName": "description",
      "row": 2,
      "column": 1,
      "rowSize": 2,
      "colSize": 2
    }
  ]
}
```

## Backward Compatibility

### **No Positioning Data** ✅
```json
{
  "columnNumber": 2,
  "fieldName": [
    {"fieldName": "field1"},
    {"fieldName": "field2"},
    {"fieldName": "field3"}
  ]
}
```
**Behavior:** Uses existing round-robin distribution (unchanged)

### **Mixed Data** ✅
```json
{
  "fieldName": [
    {"fieldName": "field1", "row": 1, "column": 1},
    {"fieldName": "field2"}
  ]
}
```
**Behavior:** Uses positioned grid layout (field2 gets defaults: row=1, column=1)

## Features

### **✅ Supported**
- Basic field positioning
- Field spanning (rowSize, colSize)
- Multi-fields in positioned layout
- SubScreen positioning
- Responsive behavior (mobile stacking)
- Automatic conflict resolution

### **🚧 Coming Soon**
- Grouped fields in positioned layout
- Advanced grid areas
- Custom gap sizing per field

## Technical Implementation

### **CSS Classes**
- `.positioned-grid`: Applied to form grids using positioning
- `.field-positioned`: Applied to individual positioned fields
- CSS custom properties for positioning: `--col-start`, `--col-size`, `--row-start`, `--row-size`

### **Component Properties**
- `usePositioning: boolean`: Indicates if positioning is active
- `positionedFields: any[]`: Array of fields with calculated positioning
- `gridRowCount: number`: Total number of rows in the grid

### **Methods**
- `hasPositioningData(fields)`: Detects if positioning should be used
- `distributeFieldsWithPositioning(fields, columnCount)`: Calculates field positions
- `getSubScreenUsePositioning(subScreenId)`: Checks SubScreen positioning mode

## Migration Guide

**No migration needed!** The system automatically detects and handles both modes:

1. **Existing forms** continue to work exactly as before
2. **New forms** can use positioning by adding row/column properties
3. **Mixed forms** automatically use positioning mode

## Responsive Behavior

- **Desktop**: Respects positioning exactly as specified
- **Mobile (≤768px)**: Stacks all fields vertically for better usability
- **Tablet**: Maintains positioning with adjusted spacing
